{"name": "<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "postinstall": "fumadocs-mdx", "lint": "biome check --write .", "lint:fix": "biome check --fix --unsafe .", "format": "biome format --write .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "list-contacts": "tsx scripts/list-contacts.ts", "content": "fumadocs-mdx", "email": "email dev --dir src/mail/templates --port 3333", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "knip": "knip"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.16", "@ai-sdk/fal": "^0.1.12", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.22", "@ai-sdk/google-vertex": "^2.2.24", "@ai-sdk/openai": "^1.1.13", "@ai-sdk/replicate": "^0.2.8", "ali-oss": "^6.21.0", "@base-ui-components/react": "1.0.0-beta.0", "@better-fetch/fetch": "^1.1.18", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.0", "@marsidev/react-turnstile": "^1.1.0", "@mendable/firecrawl-js": "^1.29.1", "@next/third-parties": "^15.3.0", "@openpanel/nextjs": "^1.0.7", "@orama/orama": "^3.1.4", "@orama/tokenizers": "^3.1.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-portal": "^1.1.4", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "0.0.33", "@react-email/render": "1.0.5", "@stripe/stripe-js": "^5.6.0", "@tabler/icons-react": "^3.31.0", "@tanstack/react-table": "^8.21.2", "@types/canvas-confetti": "^1.9.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@widgetbot/react-embed": "^1.9.0", "ai": "^4.1.45", "better-auth": "^1.1.19", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "cookie": "^1.0.2", "crisp-sdk-web": "^1.0.25", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "dotenv": "^16.4.7", "dotted-map": "^2.2.3", "drizzle-orm": "^0.39.3", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.7", "fumadocs-core": "^15.5.3", "fumadocs-mdx": "^11.6.8", "fumadocs-ui": "^15.5.3", "inngest": "^3.40.1", "input-otp": "^1.4.2", "lucide-react": "^0.483.0", "motion": "^12.4.3", "next": "15.2.1", "next-intl": "^4.0.0", "next-safe-action": "^7.10.4", "next-themes": "^0.4.4", "postgres": "^3.4.5", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-remove-scroll": "^2.6.3", "react-resizable-panels": "^2.1.7", "react-tweet": "^3.2.2", "react-use-measure": "^2.1.7", "recharts": "^2.15.1", "resend": "^4.4.1", "s3mini": "^0.2.0", "shiki": "^2.4.2", "sonner": "^2.0.0", "stripe": "^17.6.0", "swiper": "^11.2.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "use-intl": "^3.26.5", "use-media": "^1.5.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.14", "@types/ali-oss": "^6.16.11", "@types/mdx": "^2.0.13", "@types/node": "^20.19.0", "@types/pg": "^8.11.11", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.4", "knip": "^5.61.2", "postcss": "^8", "react-email": "3.0.7", "tailwindcss": "^4.0.14", "tsx": "^4.19.3", "typescript": "^5.8.3"}}