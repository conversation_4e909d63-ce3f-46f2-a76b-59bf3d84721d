---
title: Quick Start
description: Getting Started with Fumadocs
image: /images/blog/post-8.png
date: "2025-03-28"
published: true
categories: [company, news]
author: huiying
---

## Introduction

Fumadocs <span className='text-fd-muted-foreground text-sm'>(Foo-ma docs)</span> is a **documentation framework** based on Next.js, designed to be fast, flexible,
and composes seamlessly into Next.js App Router.

Fumadocs has different parts:

<Cards>

<Card icon={<CpuIcon className="text-purple-300" />} title='Fumadocs Core'>

Handles most of the logic, including document search, content source adapters, and Markdown extensions.

</Card>

<Card icon={<PanelsTopLeft className="text-blue-300" />} title='Fumadocs UI'>

The default theme of Fumadocs offers a beautiful look for documentation sites and interactive components.

</Card>

<Card icon={<Database />} title='Content Source'>

The source of your content, can be a CMS or local data layers like [Content Collections](https://www.content-collections.dev) and [Fumadocs MDX](/docs/mdx), the official content source.

</Card>

<Card icon={<Terminal />} title='Fumadocs CLI'>

A command line tool to install UI components and automate things, useful for customizing layouts.

</Card>

</Cards>

<Callout title="Want to learn more?">
  Read our in-depth [What is Fumadocs](/docs/what-is-fumadocs) introduction.
</Callout>

### Terminology

**Markdown/MDX:** Markdown is a markup language for creating formatted text. Fumadocs supports Markdown and MDX (superset of Markdown) out-of-the-box.

Although not required, some basic knowledge of Next.js App Router would be useful for further customisations.

## Automatic Installation

A minimum version of Node.js 18 required, note that Node.js 23.1 might have problems with Next.js production build.

<Tabs groupId='package-manager' persist items={['npm', 'pnpm', 'yarn', 'bun']}>

```bash tab="npm"
npm create fumadocs-app
```

```bash tab="pnpm"
pnpm create fumadocs-app
```

```bash tab="yarn"
yarn create fumadocs-app
```

```bash tab="bun"
bun create fumadocs-app
```

</Tabs>

It will ask you the framework and content source to use, a new fumadocs app should be initialized. Now you can start hacking!

<Callout title='From Existing Codebase?'>

    You can follow the [Manual Installation](/docs/manual-installation) guide to get started.

</Callout>

### Enjoy!

Create your first MDX file in the docs folder.

```mdx title="content/docs/index.mdx"
---
title: Hello World
---

## Yo what's up
```

Run the app in development mode and see http://localhost:3000/docs.

```mdx
npm run dev
```

## Explore

In the project, you can see:

- `lib/source.ts`: Code for content source adapter, [`loader()`](/docs/headless/source-api) provides an interface to interact with your content source, and assigns URL to your pages.
- `app/layout.config.tsx`: Shared options for layouts, optional but preferred to keep.

| Route                     | Description                                            |
| ------------------------- | ------------------------------------------------------ |
| `app/(home)`              | The route group for your landing page and other pages. |
| `app/docs`                | The documentation layout and pages.                    |
| `app/api/search/route.ts` | The Route Handler for search.                          |

### Writing Content

For authoring docs, make sure to read:

<Cards>
  <Card href="/docs/markdown" title="Markdown">
    Fumadocs has some additional features for authoring content too.
  </Card>
  <Card href="/docs/navigation" title="Navigation">
    Learn how to customise navigation links/sidebar items.
  </Card>
</Cards>

### Content Source

Content source handles all your content, like compiling Markdown files and validating frontmatter.

<Tabs items={['Fumadocs MDX', 'Custom Source']}>

    <Tab value='Fumadocs MDX'>

        Read the [Introduction](/docs/mdx) to learn how it handles your content.

        A `source.config.ts` config file has been included, you can customise different options like frontmatter schema.

    </Tab>

    <Tab value='Custom Source'>

        Fumadocs is not Markdown-exclusive. For other sources like Sanity, you can build a [custom content source](/docs/headless/custom-source).

    </Tab>

</Tabs>

### Customise UI

See [Customisation Guide](/docs/customisation).

## FAQ

Some common questions you may encounter.

<Accordions>
    <Accordion id='fix-monorepo-styling' title="How to fix stylings not being applied in Monorepo?">

Sometimes, `fumadocs-ui` is not installed in the workspace of your Tailwind CSS configuration file. (e.g. a monorepo setup).

You have to ensure the `fumadocs-ui` package is scanned by Tailwind CSS, and give a correct relative path to `@source`.

For example, add `../../` to point to the `node_modules` folder in root workspace.

        ```css
        @import 'tailwindcss';
        @import 'fumadocs-ui/css/neutral.css';
        @import 'fumadocs-ui/css/preset.css';

        /* [!code --] */
        @source '../node_modules/fumadocs-ui/dist/**/*.js';
        /* [!code ++] */
        @source '../../../node_modules/fumadocs-ui/dist/**/*.js';
        ```

    </Accordion>
    <Accordion id='change-base-url' title="How to change the base route of /docs?">

You can change the base route of docs (e.g. from `/docs/page` to `/info/page`).
Since Fumadocs uses Next.js App Router, you can simply rename the route:

<Files>
  <Folder name="app/docs" defaultOpen className="opacity-50" disabled>
    <File name="layout.tsx" />
  </Folder>
  <Folder name="app/info" defaultOpen>
    <File name="layout.tsx" />
  </Folder>
</Files>

And tell Fumadocs to use the new route in `source.ts`:

```ts title="lib/source.ts"
import { loader } from 'fumadocs-core/source';

export const source = loader({
  baseUrl: '/info',
  // other options
});
```

    </Accordion>
    <Accordion id='dynamic-route' title="It uses Dynamic Route, will it be poor in performance?">

Next.js turns dynamic route into static routes when `generateStaticParams` is configured.
Hence, it is as fast as static pages.

You can enable Static Exports on Next.js to get a static build output. (Notice that Route Handler doesn't work with static export, you have to configure static search)

    </Accordion>
    <Accordion id='custom-layout-docs-page' title='How to create a page in /docs without docs layout?'>

Same as managing layouts in Next.js App Router, remove the original MDX file from content directory (`/content/docs`).
This ensures duplicated pages will not cause errors.

Now, You can add the page to another route group, which isn't a descendant of docs layout.

For example, under your `app` folder:

<Files>
  <File name="(home)/docs/page.tsx" />
  <Folder name="docs">
    <File name="layout.tsx" />
    <File name="[[...slug]]/page.tsx" />
  </Folder>
</Files>

will replace the `/docs` page with your `page.tsx`.

    </Accordion>

    <Accordion id='multi-versions' title="How to implement docs with multi-version?">
        Use a separate deployment for each version.

        On Vercel, this can be done by creating another branch for a specific version on your GitHub repository.
        To link to the sites of other versions, use the Links API or a custom navigation component.
    </Accordion>

    <Accordion id='multi-docs' title="How to implement multi-docs?">
        We recommend to use [Sidebar Tabs](/docs/navigation/sidebar#sidebar-tabs).
    </Accordion>

</Accordions>

## Learn More

New to here? Don't worry, we are welcome for your questions.

If you find anything confusing, please give your feedback on [Github Discussion](https://github.com/fuma-nama/fumadocs/discussions)!

<Cards>
  <Card
    href="/docs/static-export"
    title="Configure Static Export"
    description="Learn how to enable static export on your docs"
  />
  <Card
    href="/docs/search"
    title="Customise Search"
    description="Learn how to customise document search"
  />
  <Card
    href="/docs/theme"
    title="Theming"
    description="Add themes to Fumadocs UI"
  />
  <Card
    href="/docs/components"
    title="Components"
    description="See all available components to enhance your docs"
  />
</Cards>
