/**
 * 自定义模特数据类型
 */
export interface CustomModel {
  id: string;
  userId: string;
  name: string;
  style: string;
  height: string;
  weight: string;
  bodyType: string;
  gender: 'female' | 'male';
  image: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 创建自定义模特的请求数据
 */
export interface CreateCustomModelRequest {
  name: string;
  style: string;
  height: string;
  weight: string;
  bodyType: string;
  gender?: 'female' | 'male';
  image: string;
  description?: string;
}

/**
 * 自定义模特API响应
 */
export interface CustomModelResponse {
  success: boolean;
  data?: CustomModel | CustomModel[];
  error?: string;
  message?: string;
}

/**
 * 转换自定义模特为试衣间模特格式
 */
export interface FittingRoomModel {
  id: string;
  name: string;
  style: string;
  height: string;
  weight: string;
  body: string;
  image: string;
  selected: boolean;
  isCustom: boolean;
  gender: 'female' | 'male';
  description: string;
}

/**
 * 将自定义模特转换为试衣间模特格式
 */
export function convertCustomModelToFittingModel(customModel: CustomModel): FittingRoomModel {
  return {
    id: customModel.id,
    name: customModel.name,
    style: customModel.style,
    height: customModel.height,
    weight: customModel.weight,
    body: customModel.bodyType,
    image: customModel.image,
    selected: false,
    isCustom: true,
    gender: customModel.gender,
    description: customModel.description || '自定义模特',
  };
}
