'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  categorizeUploadError,
  getErrorTranslationKey,
} from '@/lib/upload-error-handler';
import { uploadFileFromBrowser } from '@/storage/client';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TestUploadPage() {
  const [modelFile, setModelFile] = useState<File | null>(null);
  const [clothingFile, setClothingFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const handleModelUpload = async () => {
    if (!modelFile) {
      toast.error('请选择模特图片');
      return;
    }

    setUploading(true);
    try {
      const result = await uploadFileFromBrowser(modelFile, 'models');
      toast.success('模特图片上传成功');
      setResults((prev) => [
        ...prev,
        {
          type: '模特图片',
          url: result.url,
          key: result.key,
          filename: modelFile.name,
          size: modelFile.size,
          folder: 'models',
        },
      ]);
    } catch (error) {
      console.error('Model upload error:', error);

      // 使用统一的错误处理
      const uploadError = categorizeUploadError(error);
      const errorMessage =
        uploadError.code === 'PERMISSION_ERROR'
          ? '存储权限不足，请联系管理员'
          : uploadError.code === 'NETWORK_ERROR'
            ? '网络连接异常，请检查网络后重试'
            : uploadError.code === 'SERVER_ERROR'
              ? '服务器异常，请稍后重试'
              : '上传失败，请重试';

      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleClothingUpload = async () => {
    if (!clothingFile) {
      toast.error('请选择服装图片');
      return;
    }

    setUploading(true);
    try {
      const result = await uploadFileFromBrowser(clothingFile, 'clothes');
      toast.success('服装图片上传成功');
      setResults((prev) => [
        ...prev,
        {
          type: '服装图片',
          url: result.url,
          key: result.key,
          filename: clothingFile.name,
          size: clothingFile.size,
          folder: 'clothes',
        },
      ]);
    } catch (error) {
      console.error('Clothing upload error:', error);

      // 使用统一的错误处理
      const uploadError = categorizeUploadError(error);
      const errorMessage =
        uploadError.code === 'PERMISSION_ERROR'
          ? '存储权限不足，请联系管理员'
          : uploadError.code === 'NETWORK_ERROR'
            ? '网络连接异常，请检查网络后重试'
            : uploadError.code === 'SERVER_ERROR'
              ? '服务器异常，请稍后重试'
              : '上传失败，请重试';

      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">OSS上传测试</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* 模特图片上传 */}
        <Card>
          <CardHeader>
            <CardTitle>模特图片上传</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="model-file">选择模特图片</Label>
              <Input
                id="model-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setModelFile(e.target.files?.[0] || null)}
              />
            </div>
            <Button
              onClick={handleModelUpload}
              disabled={!modelFile || uploading}
              className="w-full"
            >
              {uploading ? '上传中...' : '上传模特图片'}
            </Button>
          </CardContent>
        </Card>

        {/* 服装图片上传 */}
        <Card>
          <CardHeader>
            <CardTitle>服装图片上传</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="clothing-file">选择服装图片</Label>
              <Input
                id="clothing-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setClothingFile(e.target.files?.[0] || null)}
              />
            </div>
            <Button
              onClick={handleClothingUpload}
              disabled={!clothingFile || uploading}
              className="w-full"
            >
              {uploading ? '上传中...' : '上传服装图片'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 上传结果 */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>上传结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{result.type}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p>
                        <strong>文件名:</strong> {result.filename}
                      </p>
                      <p>
                        <strong>大小:</strong> {(result.size / 1024).toFixed(2)}{' '}
                        KB
                      </p>
                      <p>
                        <strong>类型:</strong> {result.type}
                      </p>
                      <p>
                        <strong>文件夹:</strong> {result.folder}
                      </p>
                      <p>
                        <strong>Storage Key:</strong> {result.key}
                      </p>
                      <p>
                        <strong>访问URL:</strong>
                      </p>
                      <a
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline break-all"
                      >
                        {result.url}
                      </a>
                    </div>
                    <div>
                      {result.url && (
                        <img
                          src={result.url}
                          alt="上传的图片"
                          className="max-w-full h-auto rounded border"
                          style={{ maxHeight: '200px' }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
