'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  createCustomModel,
  deleteCustomModel,
  getCustomModels,
} from '@/lib/custom-models-api';
import { getBodyTypeOptions, getStyleOptions } from '@/lib/model-translations';
import type { CustomModel } from '@/types/custom-model';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function TestCustomModelsPage() {
  const [models, setModels] = useState<CustomModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    style: '',
    height: '',
    weight: '',
    bodyType: '',
    gender: 'female' as 'female' | 'male',
    image: '',
    description: '',
  });

  // 加载自定义模特列表
  const loadModels = async () => {
    try {
      setLoading(true);
      const customModels = await getCustomModels();
      setModels(customModels);
      toast.success(`加载了 ${customModels.length} 个自定义模特`);
    } catch (error) {
      console.error('加载自定义模特失败:', error);
      toast.error(
        '加载自定义模特失败: ' +
          (error instanceof Error ? error.message : '未知错误')
      );
    } finally {
      setLoading(false);
    }
  };

  // 创建新模特
  const handleCreate = async () => {
    try {
      setLoading(true);

      // 简单验证
      if (
        !formData.name ||
        !formData.style ||
        !formData.height ||
        !formData.weight ||
        !formData.bodyType
      ) {
        toast.error('请填写所有必填字段');
        return;
      }

      // 如果没有图片，使用一个测试图片URL
      const modelData = {
        ...formData,
        image:
          formData.image ||
          'https://via.placeholder.com/300x400/purple/white?text=Test+Model',
      };

      const newModel = await createCustomModel(modelData);
      setModels((prev) => [newModel, ...prev]);

      // 重置表单
      setFormData({
        name: '',
        style: '',
        height: '',
        weight: '',
        bodyType: '',
        gender: 'female',
        image: '',
        description: '',
      });

      toast.success('自定义模特创建成功！');
    } catch (error) {
      console.error('创建自定义模特失败:', error);
      toast.error(
        '创建失败: ' + (error instanceof Error ? error.message : '未知错误')
      );
    } finally {
      setLoading(false);
    }
  };

  // 删除模特
  const handleDelete = async (modelId: string) => {
    try {
      setLoading(true);
      await deleteCustomModel(modelId);
      setModels((prev) => prev.filter((m) => m.id !== modelId));
      toast.success('模特删除成功！');
    } catch (error) {
      console.error('删除模特失败:', error);
      toast.error(
        '删除失败: ' + (error instanceof Error ? error.message : '未知错误')
      );
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取模特列表
  useEffect(() => {
    loadModels();
  }, []);

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">自定义模特管理测试</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 创建新模特表单 */}
        <Card>
          <CardHeader>
            <CardTitle>创建新模特</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">模特姓名 *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="输入模特姓名"
              />
            </div>

            <div>
              <Label htmlFor="style">风格 *</Label>
              <Select
                value={formData.style}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, style: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择风格" />
                </SelectTrigger>
                <SelectContent>
                  {getStyleOptions().map(({ value }) => (
                    <SelectItem key={value} value={value}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="height">身高 *</Label>
                <Input
                  id="height"
                  value={formData.height}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, height: e.target.value }))
                  }
                  placeholder="如: 170cm"
                />
              </div>
              <div>
                <Label htmlFor="weight">体重 *</Label>
                <Input
                  id="weight"
                  value={formData.weight}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, weight: e.target.value }))
                  }
                  placeholder="如: 55kg"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="bodyType">体型 *</Label>
              <Select
                value={formData.bodyType}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, bodyType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择体型" />
                </SelectTrigger>
                <SelectContent>
                  {getBodyTypeOptions().map(({ value }) => (
                    <SelectItem key={value} value={value}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="gender">性别</Label>
              <Select
                value={formData.gender}
                onValueChange={(value: 'female' | 'male') =>
                  setFormData((prev) => ({ ...prev, gender: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="female">女性</SelectItem>
                  <SelectItem value="male">男性</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="image">图片URL（可选）</Label>
              <Input
                id="image"
                value={formData.image}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, image: e.target.value }))
                }
                placeholder="留空将使用测试图片"
              />
            </div>

            <div>
              <Label htmlFor="description">描述（可选）</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="模特描述"
              />
            </div>

            <Button
              onClick={handleCreate}
              disabled={loading}
              className="w-full"
            >
              {loading ? '创建中...' : '创建模特'}
            </Button>
          </CardContent>
        </Card>

        {/* 模特列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              自定义模特列表
              <Button
                onClick={loadModels}
                disabled={loading}
                variant="outline"
                size="sm"
              >
                {loading ? '加载中...' : '刷新'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {models.length === 0 ? (
              <p className="text-gray-500 text-center py-8">暂无自定义模特</p>
            ) : (
              <div className="space-y-4">
                {models.map((model) => (
                  <div key={model.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold">{model.name}</h3>
                        <p className="text-sm text-gray-600">
                          风格: {model.style}
                        </p>
                        <p className="text-sm text-gray-600">
                          身高: {model.height} | 体重: {model.weight}
                        </p>
                        <p className="text-sm text-gray-600">
                          体型: {model.bodyType} | 性别:{' '}
                          {model.gender === 'female' ? '女性' : '男性'}
                        </p>
                        {model.description && (
                          <p className="text-sm text-gray-600 mt-1">
                            {model.description}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-2">
                          创建时间: {new Date(model.createdAt).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        {model.image && (
                          <img
                            src={model.image}
                            alt={model.name}
                            className="w-16 h-20 object-cover rounded border"
                          />
                        )}
                        <Button
                          onClick={() => handleDelete(model.id)}
                          disabled={loading}
                          variant="destructive"
                          size="sm"
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
