import { uploadToOss, validateImageFile } from '@/lib/oss-client';
import { getStorageProvider } from '@/storage';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * 图片上传API接口
 * 支持模特图片和服装图片的上传
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const type = formData.get('type') as string | null; // 'model' 或 'clothing'

    // 验证文件是否存在
    if (!file) {
      return NextResponse.json(
        {
          success: false,
          error: '请选择要上传的文件',
        },
        { status: 400 }
      );
    }

    // 验证上传类型
    if (!type || !['model', 'clothing', 'fitting-room'].includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: '请指定正确的上传类型 (model, clothing 或 fitting-room)',
        },
        { status: 400 }
      );
    }

    // 验证文件格式和大小
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return NextResponse.json(
        {
          success: false,
          error: validation.error,
        },
        { status: 400 }
      );
    }

    // 根据类型确定存储文件夹
    const folder =
      type === 'model'
        ? 'models'
        : type === 'fitting-room'
          ? 'fitting-room'
          : 'clothes';

    // 首先尝试使用 OSS 直接上传
    try {
      const uploadResult = await uploadToOss(file, file.name, folder);

      if (uploadResult.success) {
        return NextResponse.json({
          success: true,
          url: uploadResult.url,
          ossKey: uploadResult.ossKey,
          filename: file.name,
          size: file.size,
          type: file.type,
          folder: folder,
        });
      }

      // OSS上传失败，记录错误但不抛出异常，直接回退
      console.log(
        'OSS upload failed, falling back to storage system:',
        uploadResult.error
      );
    } catch (ossError) {
      // OSS初始化或其他错误，记录并回退
      console.log(
        'OSS upload error, falling back to storage system:',
        ossError
      );
    }

    // 回退到现有的存储系统
    try {
      const storage = getStorageProvider();

      // 将 File 转换为 Buffer
      const arrayBuffer = await file.arrayBuffer();
      const fileBuffer = Buffer.from(arrayBuffer);

      const storageResult = await storage.uploadFile({
        file: fileBuffer,
        filename: file.name,
        contentType: file.type,
        folder: folder,
      });

      return NextResponse.json({
        success: true,
        url: storageResult.url,
        ossKey: storageResult.key, // 使用 ossKey 保持一致性
        key: storageResult.key, // 保留 key 字段以兼容
        filename: file.name,
        size: file.size,
        type: file.type,
        folder: folder,
      });
    } catch (storageError) {
      console.error('Both OSS and storage upload failed:', storageError);
      throw storageError;
    }
  } catch (error) {
    console.error('上传API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误，请稍后重试',
      },
      { status: 500 }
    );
  }
}

/**
 * 获取上传配置信息
 */
export async function GET() {
  return NextResponse.json({
    maxFileSize: '10MB',
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    supportedFolders: ['models', 'clothes'],
    endpoint: '/api/upload',
  });
}

// 设置请求体大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};
