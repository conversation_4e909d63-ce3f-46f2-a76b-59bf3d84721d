import { getDb } from '@/db';
import { customModel } from '@/db/schema';
import { getSession } from '@/lib/server';
import { and, desc, eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * 获取用户的自定义模特列表
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const db = await getDb();
    const models = await db
      .select()
      .from(customModel)
      .where(
        and(
          eq(customModel.userId, session.user.id),
          eq(customModel.isActive, true)
        )
      )
      .orderBy(desc(customModel.createdAt));

    return NextResponse.json({
      success: true,
      data: models,
    });
  } catch (error) {
    console.error('获取自定义模特失败:', error);
    return NextResponse.json({ error: '获取自定义模特失败' }, { status: 500 });
  }
}

/**
 * 创建新的自定义模特
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const {
      name,
      style,
      height,
      weight,
      bodyType,
      gender,
      image,
      description,
    } = body;

    // 验证必填字段
    if (!name || !style || !height || !weight || !bodyType || !image) {
      return NextResponse.json(
        { error: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    // 验证字段长度
    if (name.length > 100) {
      return NextResponse.json(
        { error: '模特名称不能超过100个字符' },
        { status: 400 }
      );
    }

    if (style.length > 50) {
      return NextResponse.json(
        { error: '风格不能超过50个字符' },
        { status: 400 }
      );
    }

    // 创建新模特
    const newModel = {
      id: nanoid(),
      userId: session.user.id,
      name: name.trim(),
      style: style.trim(),
      height: height.trim(),
      weight: weight.trim(),
      bodyType: bodyType.trim(),
      gender: gender || 'female',
      image: image.trim(),
      description: description?.trim() || '自定义模特',
      isActive: true,
    };

    const db = await getDb();
    const [createdModel] = await db
      .insert(customModel)
      .values(newModel)
      .returning();

    return NextResponse.json({
      success: true,
      data: createdModel,
    });
  } catch (error) {
    console.error('创建自定义模特失败:', error);
    return NextResponse.json({ error: '创建自定义模特失败' }, { status: 500 });
  }
}

/**
 * 删除自定义模特（软删除）
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('id');

    if (!modelId) {
      return NextResponse.json({ error: '请提供模特ID' }, { status: 400 });
    }

    // 验证模特是否属于当前用户
    const db = await getDb();
    const existingModel = await db
      .select()
      .from(customModel)
      .where(
        and(
          eq(customModel.id, modelId),
          eq(customModel.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingModel.length === 0) {
      return NextResponse.json(
        { error: '模特不存在或无权限删除' },
        { status: 404 }
      );
    }

    // 软删除（设置为不活跃）
    await db
      .update(customModel)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(customModel.id, modelId),
          eq(customModel.userId, session.user.id)
        )
      );

    return NextResponse.json({
      success: true,
      message: '模特删除成功',
    });
  } catch (error) {
    console.error('删除自定义模特失败:', error);
    return NextResponse.json({ error: '删除自定义模特失败' }, { status: 500 });
  }
}
