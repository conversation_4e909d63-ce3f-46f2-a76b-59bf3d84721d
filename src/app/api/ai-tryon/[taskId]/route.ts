import { getDb } from '@/db';
import { tryonHistory } from '@/db/schema';
import { getSession } from '@/lib/server';
import { and, eq } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * 查询AI试衣任务状态
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ taskId: string }> }
) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { taskId } = await params;

    if (!taskId) {
      return NextResponse.json({ error: '缺少任务ID' }, { status: 400 });
    }

    // 检查环境变量
    const apiKey = process.env.DASHSCOPE_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: '服务配置错误：缺少API密钥' },
        { status: 500 }
      );
    }

    // 从数据库查询任务记录
    const db = await getDb();
    const [historyRecord] = await db
      .select()
      .from(tryonHistory)
      .where(
        and(
          eq(tryonHistory.taskId, taskId),
          eq(tryonHistory.userId, session.user.id)
        )
      )
      .limit(1);

    if (!historyRecord) {
      return NextResponse.json(
        { error: '任务不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 如果任务已完成，直接返回结果
    if (
      historyRecord.taskStatus === 'SUCCEEDED' &&
      historyRecord.resultImageUrl
    ) {
      return NextResponse.json({
        success: true,
        taskId,
        taskStatus: 'SUCCEEDED',
        resultImageUrl: historyRecord.resultImageUrl,
        completedAt: historyRecord.completedAt,
      });
    }

    // 查询阿里云百炼任务状态
    const response = await fetch(
      `https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      console.error('查询AI试衣任务状态失败:', response.status, errorData);
      return NextResponse.json(
        { error: '查询任务状态失败，请稍后重试' },
        { status: 500 }
      );
    }

    const result = await response.json();
    const taskStatus = result.output?.task_status;
    const imageUrl = result.output?.image_url;

    // 更新数据库中的任务状态
    const updateData: any = {
      taskStatus: taskStatus || 'UNKNOWN',
      updatedAt: new Date(),
    };

    if (imageUrl) {
      updateData.originalResultUrl = imageUrl;
    }

    if (taskStatus === 'SUCCEEDED' || taskStatus === 'FAILED') {
      updateData.completedAt = new Date();
    }

    await db
      .update(tryonHistory)
      .set(updateData)
      .where(eq(tryonHistory.id, historyRecord.id));

    return NextResponse.json({
      success: true,
      taskId,
      taskStatus: taskStatus || 'UNKNOWN',
      imageUrl: imageUrl || null,
      message: getStatusMessage(taskStatus),
    });
  } catch (error) {
    console.error('查询AI试衣任务状态错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误，请稍后重试' },
      { status: 500 }
    );
  }
}

/**
 * 获取任务状态对应的消息
 */
function getStatusMessage(status: string): string {
  switch (status) {
    case 'PENDING':
      return '任务排队中...';
    case 'PRE-PROCESSING':
      return '前置处理中...';
    case 'RUNNING':
      return 'AI正在生成试衣效果...';
    case 'POST-PROCESSING':
      return '后置处理中...';
    case 'SUCCEEDED':
      return '试衣完成！';
    case 'FAILED':
      return '试衣失败，请重试';
    case 'CANCELED':
      return '任务已取消';
    default:
      return '处理中...';
  }
}
