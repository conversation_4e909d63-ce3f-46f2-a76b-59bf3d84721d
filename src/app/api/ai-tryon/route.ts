import { getDb } from '@/db';
import { tryonHistory } from '@/db/schema';
import { getSession } from '@/lib/server';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * AI试衣接口 - 提交试衣任务
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const {
      modelId,
      modelName,
      modelImageUrl,
      modelType,
      clothingType,
      topImageUrl,
      bottomImageUrl,
      onepieceImageUrl,
    } = body;

    // 验证必填字段
    if (!modelName || !modelImageUrl || !clothingType) {
      return NextResponse.json(
        { error: '缺少必填字段：模特信息和服装类型' },
        { status: 400 }
      );
    }

    // 验证服装图片
    if (clothingType === 'topBottom') {
      if (!topImageUrl || !bottomImageUrl) {
        return NextResponse.json(
          { error: '上下装模式需要提供上装和下装图片' },
          { status: 400 }
        );
      }
    } else if (clothingType === 'onepiece') {
      if (!onepieceImageUrl) {
        return NextResponse.json(
          { error: '连衣裙模式需要提供连衣裙图片' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json({ error: '无效的服装类型' }, { status: 400 });
    }

    // 检查环境变量
    const apiKey = process.env.DASHSCOPE_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: '服务配置错误：缺少API密钥' },
        { status: 500 }
      );
    }

    // 构建AI试衣请求参数
    const aiTryonRequest: any = {
      model: 'aitryon-plus',
      input: {
        person_image_url: modelImageUrl,
      },
      parameters: {
        resolution: -1,
        restore_face: true,
      },
    };

    // 根据服装类型添加服装图片
    if (clothingType === 'topBottom') {
      aiTryonRequest.input.top_garment_url = topImageUrl;
      aiTryonRequest.input.bottom_garment_url = bottomImageUrl;
    } else if (clothingType === 'onepiece') {
      aiTryonRequest.input.top_garment_url = onepieceImageUrl;
      // 连衣裙作为上装输入，下装置空
    }

    // 调用阿里云百炼API
    const response = await fetch(
      'https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
          'X-DashScope-Async': 'enable',
        },
        body: JSON.stringify(aiTryonRequest),
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      console.error('AI试衣API调用失败:', response.status, errorData);
      return NextResponse.json(
        { error: 'AI试衣服务暂时不可用，请稍后重试' },
        { status: 500 }
      );
    }

    const result = await response.json();
    const taskId = result.output?.task_id;

    if (!taskId) {
      console.error('AI试衣API返回格式错误:', result);
      return NextResponse.json(
        { error: 'AI试衣服务返回格式错误' },
        { status: 500 }
      );
    }

    // 保存试衣记录到数据库
    const db = await getDb();
    const [historyRecord] = await db
      .insert(tryonHistory)
      .values({
        userId: session.user.id,
        modelId: modelId || null,
        modelName,
        modelImageUrl,
        modelType: modelType || 'default',
        clothingType,
        topImageUrl: topImageUrl || null,
        bottomImageUrl: bottomImageUrl || null,
        onepieceImageUrl: onepieceImageUrl || null,
        taskId,
        taskStatus: 'PENDING',
      })
      .returning();

    return NextResponse.json({
      success: true,
      taskId,
      historyId: historyRecord.id,
      message: '试衣任务已提交，正在处理中...',
    });
  } catch (error) {
    console.error('AI试衣接口错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误，请稍后重试' },
      { status: 500 }
    );
  }
}
