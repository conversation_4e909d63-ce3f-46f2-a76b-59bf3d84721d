import { getDb } from '@/db';
import { tryonHistory } from '@/db/schema';
import { uploadToOss } from '@/lib/oss-client';
import { getSession } from '@/lib/server';
import { and, eq } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * 保存AI试衣结果到OSS
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const { taskId, imageUrl } = body;

    if (!taskId || !imageUrl) {
      return NextResponse.json(
        { error: '缺少必填字段：任务ID和图片URL' },
        { status: 400 }
      );
    }

    // 从数据库查询任务记录
    const db = await getDb();
    const [historyRecord] = await db
      .select()
      .from(tryonHistory)
      .where(
        and(
          eq(tryonHistory.taskId, taskId),
          eq(tryonHistory.userId, session.user.id)
        )
      )
      .limit(1);

    if (!historyRecord) {
      return NextResponse.json(
        { error: '任务不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 如果已经保存过，直接返回
    if (historyRecord.resultImageUrl) {
      return NextResponse.json({
        success: true,
        resultImageUrl: historyRecord.resultImageUrl,
        message: '试衣结果已保存',
      });
    }

    // 下载AI生成的图片
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      return NextResponse.json(
        { error: '无法下载AI生成的图片' },
        { status: 400 }
      );
    }

    const imageBuffer = await imageResponse.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);

    // 生成文件名
    const timestamp = Date.now();
    const fileName = `tryon-result-${taskId}-${timestamp}.jpg`;

    try {
      // 上传到OSS
      const uploadResult = await uploadToOss(buffer, fileName, 'tryon-results');

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '上传到OSS失败');
      }

      // 更新数据库记录
      await db
        .update(tryonHistory)
        .set({
          resultImageUrl: uploadResult.url,
          originalResultUrl: imageUrl,
          updatedAt: new Date(),
        })
        .where(eq(tryonHistory.id, historyRecord.id));

      return NextResponse.json({
        success: true,
        resultImageUrl: uploadResult.url,
        message: '试衣结果已保存到相册',
      });
    } catch (ossError) {
      console.error('上传到OSS失败:', ossError);

      // OSS上传失败，但仍然保存原始URL到数据库
      await db
        .update(tryonHistory)
        .set({
          originalResultUrl: imageUrl,
          updatedAt: new Date(),
        })
        .where(eq(tryonHistory.id, historyRecord.id));

      return NextResponse.json(
        {
          error: '保存到相册失败，但试衣结果仍可查看',
          imageUrl: imageUrl,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('保存试衣结果错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误，请稍后重试' },
      { status: 500 }
    );
  }
}
