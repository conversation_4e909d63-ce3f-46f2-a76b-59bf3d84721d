import { uploadFile } from '@/storage';
import { StorageError } from '@/storage/types';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const folder = formData.get('folder') as string | null;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size exceeds the 10MB limit' },
        { status: 400 }
      );
    }

    // Validate file type (optional, based on your requirements)
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not supported' },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to storage
    const result = await uploadFile(
      buffer,
      file.name,
      file.type,
      folder || undefined
    );

    console.log('uploadFile, result', result);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error uploading file:', error);

    // 详细的错误分类和处理
    let errorMessage = 'Something went wrong while uploading the file';
    let statusCode = 500;

    if (error instanceof StorageError) {
      errorMessage = error.message;

      // 根据错误信息确定更具体的状态码
      if (
        error.message.includes('403') ||
        error.message.includes('forbidden')
      ) {
        statusCode = 403;
        errorMessage =
          'Storage permission denied. Please check your credentials.';
      } else if (
        error.message.includes('404') ||
        error.message.includes('not found')
      ) {
        statusCode = 404;
        errorMessage =
          'Storage bucket not found. Please check your configuration.';
      } else if (
        error.message.includes('network') ||
        error.message.includes('connection')
      ) {
        statusCode = 503;
        errorMessage =
          'Network error. Please check your connection and try again.';
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      {
        error: errorMessage,
        code:
          statusCode === 403
            ? 'PERMISSION_ERROR'
            : statusCode === 404
              ? 'CONFIG_ERROR'
              : statusCode === 503
                ? 'NETWORK_ERROR'
                : 'SERVER_ERROR',
      },
      { status: statusCode }
    );
  }
}

// Increase the body size limit for file uploads (default is 4MB)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};
