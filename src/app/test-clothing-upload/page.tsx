'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { uploadFittingRoomImage, uploadClothingImage } from '@/lib/oss-upload-client';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TestClothingUploadPage() {
  const [topFile, setTopFile] = useState<File | null>(null);
  const [bottomFile, setBottomFile] = useState<File | null>(null);
  const [onepieceFile, setOnepieceFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const handleUpload = async (file: File, type: string) => {
    if (!file) {
      toast.error('请选择文件');
      return;
    }

    setUploading(true);

    try {
      console.log(`开始上传${type}:`, file.name, file.size, file.type);
      
      // 使用试衣间图片上传函数
      const uploadResult = await uploadFittingRoomImage(file);
      console.log(`${type}上传结果:`, uploadResult);
      
      if (uploadResult.success) {
        toast.success(`${type}上传成功！`);
        setResults(prev => [...prev, { type, ...uploadResult }]);
      } else {
        toast.error(uploadResult.error || `${type}上传失败`);
        setResults(prev => [...prev, { type, ...uploadResult }]);
      }
    } catch (error) {
      console.error(`${type}上传错误:`, error);
      toast.error(`${type}上传出错: ` + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploading(false);
    }
  };

  const handleClothingUpload = async (file: File, type: string) => {
    if (!file) {
      toast.error('请选择文件');
      return;
    }

    setUploading(true);

    try {
      console.log(`开始上传${type}:`, file.name, file.size, file.type);
      
      // 使用服装图片上传函数
      const uploadResult = await uploadClothingImage(file);
      console.log(`${type}上传结果:`, uploadResult);
      
      if (uploadResult.success) {
        toast.success(`${type}上传成功！`);
        setResults(prev => [...prev, { type, ...uploadResult }]);
      } else {
        toast.error(uploadResult.error || `${type}上传失败`);
        setResults(prev => [...prev, { type, ...uploadResult }]);
      }
    } catch (error) {
      console.error(`${type}上传错误:`, error);
      toast.error(`${type}上传出错: ` + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">服装图片上传测试</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* 试衣间上传测试 */}
        <Card>
          <CardHeader>
            <CardTitle>试衣间图片上传（上装、下装、连体）</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="top-file">上装图片</Label>
              <Input
                id="top-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setTopFile(e.target.files?.[0] || null)}
              />
              <Button 
                onClick={() => topFile && handleUpload(topFile, '上装')} 
                disabled={!topFile || uploading}
                className="w-full mt-2"
                size="sm"
              >
                {uploading ? '上传中...' : '上传上装'}
              </Button>
            </div>
            
            <div>
              <Label htmlFor="bottom-file">下装图片</Label>
              <Input
                id="bottom-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setBottomFile(e.target.files?.[0] || null)}
              />
              <Button 
                onClick={() => bottomFile && handleUpload(bottomFile, '下装')} 
                disabled={!bottomFile || uploading}
                className="w-full mt-2"
                size="sm"
              >
                {uploading ? '上传中...' : '上传下装'}
              </Button>
            </div>
            
            <div>
              <Label htmlFor="onepiece-file">连体服装图片</Label>
              <Input
                id="onepiece-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setOnepieceFile(e.target.files?.[0] || null)}
              />
              <Button 
                onClick={() => onepieceFile && handleUpload(onepieceFile, '连体服装')} 
                disabled={!onepieceFile || uploading}
                className="w-full mt-2"
                size="sm"
              >
                {uploading ? '上传中...' : '上传连体服装'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 服装库上传测试 */}
        <Card>
          <CardHeader>
            <CardTitle>服装库图片上传</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="clothing-file">服装图片</Label>
              <Input
                id="clothing-file"
                type="file"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={(e) => setOnepieceFile(e.target.files?.[0] || null)}
              />
              <Button 
                onClick={() => onepieceFile && handleClothingUpload(onepieceFile, '服装库图片')} 
                disabled={!onepieceFile || uploading}
                className="w-full mt-2"
              >
                {uploading ? '上传中...' : '上传到服装库'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 上传结果 */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>上传结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{result.type}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p><strong>成功:</strong> {result.success ? '是' : '否'}</p>
                      {result.success ? (
                        <>
                          <p><strong>文件名:</strong> {result.filename}</p>
                          <p><strong>大小:</strong> {result.size} bytes</p>
                          <p><strong>类型:</strong> {result.type}</p>
                          <p><strong>文件夹:</strong> {result.folder}</p>
                          <p><strong>Key:</strong> {result.ossKey || result.key}</p>
                          <p><strong>访问URL:</strong></p>
                          <a 
                            href={result.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline break-all"
                          >
                            {result.url}
                          </a>
                        </>
                      ) : (
                        <p><strong>错误:</strong> {result.error}</p>
                      )}
                    </div>
                    <div>
                      {result.success && result.url && (
                        <img 
                          src={result.url} 
                          alt="上传的图片" 
                          className="max-w-full h-auto rounded border"
                          style={{ maxHeight: '200px' }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
