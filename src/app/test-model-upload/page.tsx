'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { uploadModelImage } from '@/lib/oss-upload-client';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TestModelUploadPage() {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleUpload = async () => {
    if (!file) {
      toast.error('请选择文件');
      return;
    }

    setUploading(true);
    setResult(null);

    try {
      console.log('开始上传文件:', file.name, file.size, file.type);
      const uploadResult = await uploadModelImage(file);
      console.log('上传结果:', uploadResult);
      
      if (uploadResult.success) {
        toast.success('上传成功！');
        setResult(uploadResult);
      } else {
        toast.error(uploadResult.error || '上传失败');
        setResult(uploadResult);
      }
    } catch (error) {
      console.error('上传错误:', error);
      toast.error('上传出错: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6">模特图片上传测试</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>上传测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="file">选择图片文件</Label>
            <Input
              id="file"
              type="file"
              accept=".jpg,.jpeg,.png,.webp"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
            />
          </div>
          
          {file && (
            <div className="text-sm text-gray-600">
              <p><strong>文件名:</strong> {file.name}</p>
              <p><strong>大小:</strong> {(file.size / 1024).toFixed(2)} KB</p>
              <p><strong>类型:</strong> {file.type}</p>
            </div>
          )}
          
          <Button 
            onClick={handleUpload} 
            disabled={!file || uploading}
            className="w-full"
          >
            {uploading ? '上传中...' : '开始上传'}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle>上传结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>成功:</strong> {result.success ? '是' : '否'}</p>
              {result.success ? (
                <>
                  <p><strong>URL:</strong> <a href={result.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline break-all">{result.url}</a></p>
                  <p><strong>OSS Key:</strong> {result.ossKey}</p>
                  <p><strong>文件名:</strong> {result.filename}</p>
                  <p><strong>大小:</strong> {result.size} bytes</p>
                  <p><strong>类型:</strong> {result.type}</p>
                  <p><strong>文件夹:</strong> {result.folder}</p>
                  
                  {result.url && (
                    <div className="mt-4">
                      <p><strong>图片预览:</strong></p>
                      <img 
                        src={result.url} 
                        alt="上传的图片" 
                        className="max-w-full h-auto rounded border mt-2"
                        style={{ maxHeight: '300px' }}
                      />
                    </div>
                  )}
                </>
              ) : (
                <p><strong>错误:</strong> {result.error}</p>
              )}
            </div>
            
            <details className="mt-4">
              <summary className="cursor-pointer font-semibold">完整响应数据</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
