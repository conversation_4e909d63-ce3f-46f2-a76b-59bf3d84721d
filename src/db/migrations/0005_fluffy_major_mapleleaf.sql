CREATE TABLE "tryon_history" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"model_id" text,
	"model_name" varchar(100) NOT NULL,
	"model_image_url" text NOT NULL,
	"model_type" varchar(20) DEFAULT 'default' NOT NULL,
	"clothing_type" varchar(20) NOT NULL,
	"top_image_url" text,
	"bottom_image_url" text,
	"onepiece_image_url" text,
	"task_id" text NOT NULL,
	"result_image_url" text,
	"original_result_url" text,
	"task_status" varchar(50) DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "tryon_history" ADD CONSTRAINT "tryon_history_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "tryon_history_user_id_idx" ON "tryon_history" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "tryon_history_task_id_idx" ON "tryon_history" USING btree ("task_id");--> statement-breakpoint
CREATE INDEX "tryon_history_created_at_idx" ON "tryon_history" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "tryon_history_task_status_idx" ON "tryon_history" USING btree ("task_status");