-- 创建试衣历史表
CREATE TABLE IF NOT EXISTS tryon_history (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  user_id TEXT NOT NULL REFERENCES user(id) ON DELETE CASCADE,
  
  -- 模特信息
  model_id TEXT, -- 如果是自定义模特，关联到 custom_model 表
  model_name VARCHAR(100) NOT NULL,
  model_image_url TEXT NOT NULL,
  model_type VARCHAR(20) NOT NULL DEFAULT 'default', -- 'default' 或 'custom'
  
  -- 服装信息
  clothing_type VARCHAR(20) NOT NULL, -- 'topBottom' 或 'onepiece'
  top_image_url TEXT, -- 上装图片URL
  bottom_image_url TEXT, -- 下装图片URL
  onepiece_image_url TEXT, -- 连衣裙图片URL
  
  -- AI试衣结果
  task_id TEXT NOT NULL, -- 阿里云百炼任务ID
  result_image_url TEXT, -- 试衣结果图片URL（保存到我们的OSS）
  original_result_url TEXT, -- AI原始结果URL（24小时有效期）
  task_status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- 任务状态
  
  -- 元数据
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP, -- 任务完成时间
  
  -- 索引
  CONSTRAINT check_clothing_type CHECK (
    (clothing_type = 'topBottom' AND top_image_url IS NOT NULL AND bottom_image_url IS NOT NULL AND onepiece_image_url IS NULL) OR
    (clothing_type = 'onepiece' AND onepiece_image_url IS NOT NULL AND top_image_url IS NULL AND bottom_image_url IS NULL)
  )
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tryon_history_user_id ON tryon_history(user_id);
CREATE INDEX IF NOT EXISTS idx_tryon_history_task_id ON tryon_history(task_id);
CREATE INDEX IF NOT EXISTS idx_tryon_history_created_at ON tryon_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tryon_history_task_status ON tryon_history(task_status);

-- 添加注释
COMMENT ON TABLE tryon_history IS 'AI试衣历史记录表';
COMMENT ON COLUMN tryon_history.user_id IS '用户ID';
COMMENT ON COLUMN tryon_history.model_id IS '自定义模特ID（如果是默认模特则为空）';
COMMENT ON COLUMN tryon_history.model_name IS '模特名称';
COMMENT ON COLUMN tryon_history.model_image_url IS '模特图片URL';
COMMENT ON COLUMN tryon_history.model_type IS '模特类型：default=默认模特，custom=自定义模特';
COMMENT ON COLUMN tryon_history.clothing_type IS '服装类型：topBottom=上下装，onepiece=连衣裙';
COMMENT ON COLUMN tryon_history.task_id IS '阿里云百炼任务ID';
COMMENT ON COLUMN tryon_history.result_image_url IS '试衣结果图片URL（保存到OSS）';
COMMENT ON COLUMN tryon_history.original_result_url IS 'AI原始结果URL（24小时有效期）';
COMMENT ON COLUMN tryon_history.task_status IS '任务状态：PENDING, RUNNING, SUCCEEDED, FAILED等';
