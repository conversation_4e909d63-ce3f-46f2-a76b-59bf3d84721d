CREATE TABLE "custom_model" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"name" varchar(100) NOT NULL,
	"style" varchar(50) NOT NULL,
	"height" varchar(20) NOT NULL,
	"weight" varchar(20) NOT NULL,
	"body_type" varchar(50) NOT NULL,
	"gender" varchar(10) DEFAULT 'female' NOT NULL,
	"image" text NOT NULL,
	"description" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "custom_model" ADD CONSTRAINT "custom_model_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "custom_model_user_id_idx" ON "custom_model" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "custom_model_is_active_idx" ON "custom_model" USING btree ("is_active");