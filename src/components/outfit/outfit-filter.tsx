'use client';

import { Button } from '@/components/ui/button';
import { Filter, Users, User } from 'lucide-react';
import { useTranslations } from 'next-intl';

export type FilterType = 'all' | 'male' | 'female';

interface OutfitFilterProps {
  currentFilter: FilterType;
  onFilterChange: (filter: FilterType) => void;
}

export function OutfitFilter({ currentFilter, onFilterChange }: OutfitFilterProps) {
  const t = useTranslations('OutfitPage.gallery.filters');

  const filters: { key: FilterType; label: string; icon: React.ReactNode }[] = [
    {
      key: 'all',
      label: t('all'),
      icon: <Users className="w-4 h-4" />,
    },
    {
      key: 'male',
      label: t('male'),
      icon: <User className="w-4 h-4" />,
    },
    {
      key: 'female',
      label: t('female'),
      icon: <User className="w-4 h-4" />,
    },
  ];

  return (
    <div className="flex items-center gap-2">
      <Filter className="w-4 h-4 text-muted-foreground" />
      <div className="flex gap-1">
        {filters.map((filter) => (
          <Button
            key={filter.key}
            variant={currentFilter === filter.key ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFilterChange(filter.key)}
            className="gap-2"
          >
            {filter.icon}
            {filter.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
