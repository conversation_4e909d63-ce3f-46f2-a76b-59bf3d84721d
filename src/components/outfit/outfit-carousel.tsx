'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { SplitImage } from '@/types/outfit';
import { ChevronLeft, ChevronRight, ExternalLink, Shirt } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

interface OutfitCarouselProps {
  isOpen: boolean;
  onClose: () => void;
  splitImages: SplitImage[];
  outfitTitle?: string;
  genderType?: string;
}

export function OutfitCarousel({
  isOpen,
  onClose,
  splitImages,
  outfitTitle = 'Outfit Details',
  genderType = 'Women',
}: OutfitCarouselProps) {
  const t = useTranslations('OutfitPage.gallery');
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % splitImages.length);
  };

  const prevImage = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + splitImages.length) % splitImages.length
    );
  };

  const handleBuyNow = () => {
    const currentImage = splitImages[currentIndex];
    if (currentImage?.amazon_url) {
      window.open(currentImage.amazon_url, '_blank');
    }
  };

  const handleTryOn = () => {
    const currentImage = splitImages[currentIndex];
    if (currentImage?.url) {
      // 判断是单件还是多件
      const isSinglePiece =
        splitImages.length === 1 ||
        currentImage.type === 'dress' ||
        currentImage.type === 'onepiece';

      if (isSinglePiece) {
        // 单件连体
        const params = new URLSearchParams({
          image: currentImage.url,
          type: 'onepiece',
        });
        window.open(`/fitting?${params.toString()}`, '_blank');
      } else {
        // 多件上下装 - 传递所有图片
        const topImage = splitImages.find((img) => img.type === 'top');
        const bottomImage = splitImages.find((img) => img.type === 'bottom');

        const params = new URLSearchParams();
        if (topImage) {
          params.append('topImage', topImage.url);
        }
        if (bottomImage) {
          params.append('bottomImage', bottomImage.url);
        }
        params.append('type', 'topBottom');

        window.open(`/fitting?${params.toString()}`, '_blank');
      }
    }
  };

  if (splitImages.length === 0) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-2xl font-bold text-center">
            {t('modal.title')}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
          {/* Left side - Image carousel */}
          <div className="space-y-4">
            <div className="relative">
              <div className="aspect-[3/4] relative overflow-hidden rounded-lg bg-gray-100 border">
                <Image
                  src={splitImages[currentIndex]?.url || ''}
                  alt={`${splitImages[currentIndex]?.type || 'Item'} ${currentIndex + 1}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />

                {/* Navigation arrows */}
                {splitImages.length > 1 && (
                  <>
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-md"
                      onClick={prevImage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-md"
                      onClick={nextImage}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>

              {/* Dots indicator */}
              {splitImages.length > 1 && (
                <div className="flex justify-center space-x-2 mt-3">
                  {splitImages.map((_, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`w-2.5 h-2.5 rounded-full transition-colors ${
                        index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                      }`}
                      onClick={() => setCurrentIndex(index)}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right side - Details and actions */}
          <div className="flex flex-col space-y-6">
            {/* Gender badge */}
            <div className="flex justify-start">
              <Badge
                variant="secondary"
                className="px-3 py-1 text-sm font-medium"
              >
                {genderType}
              </Badge>
            </div>

            {/* Title */}
            <div>
              <h2 className="text-3xl font-bold mb-2">
                {splitImages[currentIndex]?.type?.toLowerCase() === 'bottom'
                  ? t('modal.itemTypes.bottom')
                  : t('modal.itemTypes.top')}
              </h2>
              <p className="text-lg text-muted-foreground">
                {t('modal.description')}
              </p>
            </div>

            {/* Item counter */}
            <div className="text-sm text-muted-foreground">
              {splitImages[currentIndex]?.type?.toLowerCase() === 'bottom'
                ? t('modal.itemTypes.bottom')
                : t('modal.itemTypes.top')}{' '}
              ({currentIndex + 1} {t('modal.of')} {splitImages.length})
            </div>

            {/* Action buttons */}
            <div className="space-y-3 mt-auto">
              <Button
                onClick={handleBuyNow}
                className="w-full h-12 text-base font-medium"
                disabled={!splitImages[currentIndex]?.amazon_url}
              >
                <ExternalLink className="w-5 h-5 mr-2" />
                {t('buttons.buyNow')}
              </Button>
              <Button
                variant="outline"
                onClick={handleTryOn}
                className="w-full h-12 text-base font-medium border-2"
              >
                <Shirt className="w-5 h-5 mr-2" />
                {t('buttons.tryOn')}
              </Button>
            </div>

            {/* Outfit Items section */}
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-3 text-left">
                {t('modal.outfitItems')} ({splitImages.length})
              </h3>
              <div className="flex justify-start gap-3">
                {splitImages.map((item, index) => (
                  <div
                    key={item.id}
                    className={`relative w-24 h-24 rounded-lg overflow-hidden border-2 cursor-pointer transition-all ${
                      index === currentIndex
                        ? 'border-primary shadow-md'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setCurrentIndex(index)}
                  >
                    <Image
                      src={item.url}
                      alt={`${item.type} ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="150px"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-2 text-center">
                      {item.type?.toLowerCase() === 'bottom'
                        ? t('modal.itemTypes.bottom')
                        : t('modal.itemTypes.top')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
