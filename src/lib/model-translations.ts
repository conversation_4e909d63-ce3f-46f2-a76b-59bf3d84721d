/**
 * 模特相关的翻译工具函数
 */

/**
 * 风格选项定义
 */
export const STYLE_OPTIONS = [
  'fashion',
  'elegant',
  'casual',
  'business',
  'sporty',
  'athletic',
  'professional',
  'vintage',
  'minimalist',
  'bohemian',
] as const;

/**
 * 体型选项定义
 */
export const BODY_TYPE_OPTIONS = [
  'rectangle',
  'strawberry',
  'pear',
  'athletic',
] as const;

/**
 * 性别选项定义
 */
export const GENDER_OPTIONS = ['female', 'male'] as const;

/**
 * 筛选选项定义
 */
export const FILTER_OPTIONS = ['all', 'female', 'male', 'custom'] as const;

export type StyleOption = (typeof STYLE_OPTIONS)[number];
export type BodyTypeOption = (typeof BODY_TYPE_OPTIONS)[number];
export type GenderOption = (typeof GENDER_OPTIONS)[number];
export type FilterOption = (typeof FILTER_OPTIONS)[number];

/**
 * 获取风格的翻译键
 */
export function getStyleTranslationKey(style: string): string {
  if (!style || !isValidStyle(style)) {
    return 'modelDialog.styles.elegant'; // 默认值
  }
  return `modelDialog.styles.${style}`;
}

/**
 * 获取体型的翻译键
 */
export function getBodyTypeTranslationKey(bodyType: string): string {
  if (!bodyType || !isValidBodyType(bodyType)) {
    return 'modelDialog.bodyTypes.rectangle'; // 默认值
  }
  return `modelDialog.bodyTypes.${bodyType}`;
}

/**
 * 获取性别的翻译键
 */
export function getGenderTranslationKey(gender: string): string {
  return `modelDialog.genders.${gender}`;
}

/**
 * 获取筛选选项的翻译键
 */
export function getFilterTranslationKey(filter: string): string {
  return `modelDialog.filters.${filter}`;
}

/**
 * 验证风格值是否有效
 */
export function isValidStyle(style: string): style is StyleOption {
  return STYLE_OPTIONS.includes(style as StyleOption);
}

/**
 * 验证体型值是否有效
 */
export function isValidBodyType(bodyType: string): bodyType is BodyTypeOption {
  return BODY_TYPE_OPTIONS.includes(bodyType as BodyTypeOption);
}

/**
 * 验证性别值是否有效
 */
export function isValidGender(gender: string): gender is GenderOption {
  return GENDER_OPTIONS.includes(gender as GenderOption);
}

/**
 * 验证筛选选项是否有效
 */
export function isValidFilter(filter: string): filter is FilterOption {
  return FILTER_OPTIONS.includes(filter as FilterOption);
}

/**
 * 获取所有风格选项的翻译键值对
 */
export function getStyleOptions(): Array<{ value: StyleOption; key: string }> {
  return STYLE_OPTIONS.map((style) => ({
    value: style,
    key: getStyleTranslationKey(style),
  }));
}

/**
 * 获取所有体型选项的翻译键值对
 */
export function getBodyTypeOptions(): Array<{
  value: BodyTypeOption;
  key: string;
}> {
  return BODY_TYPE_OPTIONS.map((bodyType) => ({
    value: bodyType,
    key: getBodyTypeTranslationKey(bodyType),
  }));
}

/**
 * 获取所有性别选项的翻译键值对
 */
export function getGenderOptions(): Array<{
  value: GenderOption;
  key: string;
}> {
  return GENDER_OPTIONS.map((gender) => ({
    value: gender,
    key: getGenderTranslationKey(gender),
  }));
}

/**
 * 获取所有筛选选项的翻译键值对
 */
export function getFilterOptions(): Array<{
  value: FilterOption;
  key: string;
}> {
  return FILTER_OPTIONS.map((filter) => ({
    value: filter,
    key: getFilterTranslationKey(filter),
  }));
}
