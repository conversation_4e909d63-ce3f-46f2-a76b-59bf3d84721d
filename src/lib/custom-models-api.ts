import type { CustomModel, CreateCustomModelRequest, CustomModelResponse } from '@/types/custom-model';

/**
 * 获取用户的自定义模特列表
 */
export async function getCustomModels(): Promise<CustomModel[]> {
  try {
    const response = await fetch('/api/custom-models', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: CustomModelResponse = await response.json();

    if (!response.ok) {
      throw new Error(result.error || '获取自定义模特失败');
    }

    return (result.data as CustomModel[]) || [];
  } catch (error) {
    console.error('获取自定义模特失败:', error);
    throw error;
  }
}

/**
 * 创建新的自定义模特
 */
export async function createCustomModel(data: CreateCustomModelRequest): Promise<CustomModel> {
  try {
    const response = await fetch('/api/custom-models', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result: CustomModelResponse = await response.json();

    if (!response.ok) {
      throw new Error(result.error || '创建自定义模特失败');
    }

    return result.data as CustomModel;
  } catch (error) {
    console.error('创建自定义模特失败:', error);
    throw error;
  }
}

/**
 * 删除自定义模特
 */
export async function deleteCustomModel(modelId: string): Promise<void> {
  try {
    const response = await fetch(`/api/custom-models?id=${modelId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: CustomModelResponse = await response.json();

    if (!response.ok) {
      throw new Error(result.error || '删除自定义模特失败');
    }
  } catch (error) {
    console.error('删除自定义模特失败:', error);
    throw error;
  }
}

/**
 * 验证自定义模特数据
 */
export function validateCustomModelData(data: Partial<CreateCustomModelRequest>): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('请输入模特姓名');
  } else if (data.name.trim().length > 100) {
    errors.push('模特姓名不能超过100个字符');
  }

  if (!data.style?.trim()) {
    errors.push('请选择风格');
  } else if (data.style.trim().length > 50) {
    errors.push('风格不能超过50个字符');
  }

  if (!data.height?.trim()) {
    errors.push('请输入身高');
  }

  if (!data.weight?.trim()) {
    errors.push('请输入体重');
  }

  if (!data.bodyType?.trim()) {
    errors.push('请选择体型');
  }

  if (!data.image?.trim()) {
    errors.push('请上传模特图片');
  }

  if (data.gender && !['female', 'male'].includes(data.gender)) {
    errors.push('性别只能是 female 或 male');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
