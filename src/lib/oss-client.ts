// 动态导入 ali-oss，避免构建时错误
let OSS: any = null;
let ossClient: any = null;

async function getOSSClient() {
  if (!ossClient) {
    try {
      // 检查环境变量
      const accessKeyId =
        process.env.OSS_ACCESS_KEY_ID || process.env.MY_OSS_ACCESS_KEY_ID;
      const accessKeySecret =
        process.env.OSS_ACCESS_KEY_SECRET ||
        process.env.MY_OSS_ACCESS_KEY_SECRET;

      if (!accessKeyId || !accessKeySecret) {
        throw new Error('OSS credentials not found in environment variables');
      }

      OSS = (await import('ali-oss')).default;

      if (!OSS) {
        throw new Error('Failed to import ali-oss module');
      }

      ossClient = new OSS({
        accessKeyId,
        accessKeySecret,
        // Bucket所在地域
        region: 'oss-cn-beijing',
        // 使用V4签名算法
        authorizationV4: true,
        // Bucket名称
        bucket: 'huiyingbucket',
        // Bucket所在地域对应的公网Endpoint
        endpoint: 'https://oss-cn-beijing.aliyuncs.com',
      });

      if (!ossClient) {
        throw new Error('Failed to create OSS client');
      }
    } catch (error) {
      console.error('Failed to initialize OSS client:', error);
      throw new Error(
        `OSS initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
  return ossClient;
}

/**
 * 生成OSS文件的公网访问URL
 * @param ossKey OSS文件键名
 * @returns 公网访问URL
 */
export function getOssUrl(ossKey: string): string {
  return `https://huiyingbucket.oss-cn-beijing.aliyuncs.com/${ossKey}`;
}

/**
 * 生成唯一的文件名
 * @param originalFilename 原始文件名
 * @param folder 文件夹路径
 * @returns 唯一的OSS键名
 */
export function generateOssKey(originalFilename: string, folder = ''): string {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 8);
  const extension = originalFilename.split('.').pop() || '';
  const filename = `${timestamp}-${randomStr}${extension ? `.${extension}` : ''}`;

  return folder ? `${folder}/${filename}` : filename;
}

/**
 * 验证文件类型
 * @param file 文件对象
 * @returns 是否为支持的图片格式
 */
export function validateImageFile(file: File): {
  valid: boolean;
  error?: string;
} {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: '不支持的文件格式，请上传 JPG、PNG 或 WEBP 格式的图片',
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: '文件大小超过限制，请上传小于 10MB 的图片',
    };
  }

  return { valid: true };
}

/**
 * 上传文件到OSS
 * @param file 文件对象或Buffer
 * @param filename 原始文件名
 * @param folder 存储文件夹
 * @returns 上传结果
 */
export async function uploadToOss(
  file: File | Buffer,
  filename: string,
  folder = ''
): Promise<{
  success: boolean;
  url?: string;
  ossKey?: string;
  error?: string;
}> {
  try {
    // 生成唯一的OSS键名
    const ossKey = generateOssKey(filename, folder);

    let fileBuffer: Buffer;

    // 如果是File对象，转换为Buffer
    if (file instanceof File) {
      const arrayBuffer = await file.arrayBuffer();
      fileBuffer = Buffer.from(arrayBuffer);
    } else {
      fileBuffer = file;
    }

    // 获取OSS客户端并上传
    const client = await getOSSClient();
    const result = await client.put(ossKey, fileBuffer);

    if (result.res?.status === 200) {
      const url = getOssUrl(ossKey);
      return {
        success: true,
        url,
        ossKey,
      };
    }

    return {
      success: false,
      error: '上传失败，请重试',
    };
  } catch (error) {
    console.error('OSS上传错误:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '上传失败，请重试',
    };
  }
}

/**
 * 从OSS删除文件
 * @param ossKey OSS文件键名
 * @returns 删除结果
 */
export async function deleteFromOss(ossKey: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const client = await getOSSClient();
    await client.delete(ossKey);
    return { success: true };
  } catch (error) {
    console.error('OSS删除错误:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '删除失败',
    };
  }
}
