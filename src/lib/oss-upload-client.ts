'use client';

/**
 * OSS上传结果接口
 */
export interface OssUploadResult {
  success: boolean;
  url?: string;
  ossKey?: string;
  filename?: string;
  size?: number;
  type?: string;
  folder?: string;
  error?: string;
}

/**
 * 从浏览器上传文件到OSS
 * @param file 文件对象
 * @param type 上传类型 ('model' 或 'clothing')
 * @returns 上传结果
 */
export const uploadFileToOss = async (
  file: File,
  type: 'model' | 'clothing'
): Promise<OssUploadResult> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || '上传失败，请重试',
      };
    }

    return result;
  } catch (error) {
    console.error('上传错误:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : '网络错误，请检查网络连接',
    };
  }
};

/**
 * 验证图片文件（客户端验证）
 * @param file 文件对象
 * @returns 验证结果
 */
export function validateImageFileClient(file: File): {
  valid: boolean;
  error?: string;
} {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: '不支持的文件格式，请上传 JPG、PNG 或 WEBP 格式的图片',
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: '文件大小超过限制，请上传小于 10MB 的图片',
    };
  }

  return { valid: true };
}

/**
 * 上传模特图片
 * @param file 图片文件
 * @returns 上传结果
 */
export const uploadModelImage = async (
  file: File
): Promise<OssUploadResult> => {
  // 客户端验证
  const validation = validateImageFileClient(file);
  if (!validation.valid) {
    return {
      success: false,
      error: validation.error,
    };
  }

  return uploadFileToOss(file, 'model');
};

/**
 * 上传服装图片
 * @param file 图片文件
 * @returns 上传结果
 */
export const uploadClothingImage = async (
  file: File
): Promise<OssUploadResult> => {
  // 客户端验证
  const validation = validateImageFileClient(file);
  if (!validation.valid) {
    return {
      success: false,
      error: validation.error,
    };
  }

  return uploadFileToOss(file, 'clothing');
};

/**
 * 上传试衣间图片（用于上装、下装、连体服装）
 * @param file 图片文件
 * @returns 上传结果
 */
export const uploadFittingRoomImage = async (
  file: File
): Promise<OssUploadResult> => {
  // 客户端验证
  const validation = validateImageFileClient(file);
  if (!validation.valid) {
    return {
      success: false,
      error: validation.error,
    };
  }

  // 使用 clothing 类型，但会被存储到 fitting-room 文件夹
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'fitting-room'); // 新的类型

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || '上传失败，请重试',
      };
    }

    return result;
  } catch (error) {
    console.error('上传错误:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : '网络错误，请检查网络连接',
    };
  }
};
