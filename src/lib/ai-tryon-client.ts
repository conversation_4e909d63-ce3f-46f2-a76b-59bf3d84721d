/**
 * AI试衣客户端函数
 */

export interface TryonRequest {
  modelId?: string;
  modelName: string;
  modelImageUrl: string;
  modelType?: 'default' | 'custom';
  clothingType: 'topBottom' | 'onepiece';
  topImageUrl?: string;
  bottomImageUrl?: string;
  onepieceImageUrl?: string;
}

export interface TryonResponse {
  success: boolean;
  taskId?: string;
  historyId?: string;
  message?: string;
  error?: string;
}

export interface TryonStatusResponse {
  success: boolean;
  taskId?: string;
  taskStatus?: string;
  imageUrl?: string;
  resultImageUrl?: string;
  completedAt?: string;
  message?: string;
  error?: string;
}

export interface SaveTryonResponse {
  success: boolean;
  resultImageUrl?: string;
  message?: string;
  error?: string;
  imageUrl?: string;
}

/**
 * 提交AI试衣任务
 */
export async function submitTryonTask(request: TryonRequest): Promise<TryonResponse> {
  try {
    const response = await fetch('/api/ai-tryon', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || '提交试衣任务失败',
      };
    }

    return data;
  } catch (error) {
    console.error('提交试衣任务错误:', error);
    return {
      success: false,
      error: '网络错误，请检查连接后重试',
    };
  }
}

/**
 * 查询AI试衣任务状态
 */
export async function getTryonTaskStatus(taskId: string): Promise<TryonStatusResponse> {
  try {
    const response = await fetch(`/api/ai-tryon/${taskId}`, {
      method: 'GET',
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || '查询任务状态失败',
      };
    }

    return data;
  } catch (error) {
    console.error('查询任务状态错误:', error);
    return {
      success: false,
      error: '网络错误，请检查连接后重试',
    };
  }
}

/**
 * 保存AI试衣结果到OSS
 */
export async function saveTryonResult(taskId: string, imageUrl: string): Promise<SaveTryonResponse> {
  try {
    const response = await fetch('/api/ai-tryon/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId,
        imageUrl,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || '保存试衣结果失败',
        imageUrl: data.imageUrl, // 可能包含原始图片URL
      };
    }

    return data;
  } catch (error) {
    console.error('保存试衣结果错误:', error);
    return {
      success: false,
      error: '网络错误，请检查连接后重试',
    };
  }
}

/**
 * 轮询查询任务状态直到完成
 */
export async function pollTryonTaskStatus(
  taskId: string,
  onStatusUpdate?: (status: TryonStatusResponse) => void,
  maxAttempts: number = 60, // 最多查询60次
  interval: number = 5000 // 每5秒查询一次
): Promise<TryonStatusResponse> {
  let attempts = 0;

  return new Promise((resolve) => {
    const poll = async () => {
      attempts++;
      
      const status = await getTryonTaskStatus(taskId);
      
      // 调用状态更新回调
      if (onStatusUpdate) {
        onStatusUpdate(status);
      }

      // 如果任务完成或失败，停止轮询
      if (
        !status.success ||
        status.taskStatus === 'SUCCEEDED' ||
        status.taskStatus === 'FAILED' ||
        status.taskStatus === 'CANCELED' ||
        attempts >= maxAttempts
      ) {
        resolve(status);
        return;
      }

      // 继续轮询
      setTimeout(poll, interval);
    };

    poll();
  });
}

/**
 * 验证试衣请求参数
 */
export function validateTryonRequest(request: TryonRequest): { valid: boolean; error?: string } {
  if (!request.modelName || !request.modelImageUrl) {
    return { valid: false, error: '请选择模特' };
  }

  if (!request.clothingType) {
    return { valid: false, error: '请选择服装类型' };
  }

  if (request.clothingType === 'topBottom') {
    if (!request.topImageUrl || !request.bottomImageUrl) {
      return { valid: false, error: '请上传上装和下装图片' };
    }
  } else if (request.clothingType === 'onepiece') {
    if (!request.onepieceImageUrl) {
      return { valid: false, error: '请上传连衣裙图片' };
    }
  } else {
    return { valid: false, error: '无效的服装类型' };
  }

  return { valid: true };
}
