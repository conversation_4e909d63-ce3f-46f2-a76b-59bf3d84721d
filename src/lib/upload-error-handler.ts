/**
 * 上传错误处理工具
 * 提供统一的错误分类和国际化错误信息
 */

export interface UploadError {
  code: string;
  message: string;
  originalError?: Error;
}

/**
 * 错误类型枚举
 */
export enum UploadErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  FILE_TYPE_ERROR = 'FILE_TYPE_ERROR', 
  FILE_SIZE_ERROR = 'FILE_SIZE_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  CONFIG_ERROR = 'CONFIG_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  MISSING_FIELDS = 'MISSING_FIELDS'
}

/**
 * 根据错误信息分类错误类型
 * @param error 原始错误
 * @returns 分类后的错误信息
 */
export function categorizeUploadError(error: any): UploadError {
  const errorMessage = error?.message || error?.toString() || '';
  const errorLower = errorMessage.toLowerCase();

  // 网络错误
  if (errorLower.includes('network') || 
      errorLower.includes('fetch') || 
      errorLower.includes('connection') ||
      errorLower.includes('timeout')) {
    return {
      code: UploadErrorCode.NETWORK_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 权限错误 (403)
  if (errorLower.includes('403') || 
      errorLower.includes('forbidden') || 
      errorLower.includes('permission') ||
      errorLower.includes('access denied')) {
    return {
      code: UploadErrorCode.PERMISSION_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 服务器错误 (5xx)
  if (errorLower.includes('500') || 
      errorLower.includes('502') || 
      errorLower.includes('503') || 
      errorLower.includes('504') ||
      errorLower.includes('server error') ||
      errorLower.includes('internal server')) {
    return {
      code: UploadErrorCode.SERVER_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 配置错误
  if (errorLower.includes('config') || 
      errorLower.includes('credential') || 
      errorLower.includes('authentication') ||
      errorLower.includes('bucket') ||
      errorLower.includes('endpoint')) {
    return {
      code: UploadErrorCode.CONFIG_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 文件类型错误
  if (errorLower.includes('file type') || 
      errorLower.includes('format') || 
      errorLower.includes('mime') ||
      errorLower.includes('extension')) {
    return {
      code: UploadErrorCode.FILE_TYPE_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 文件大小错误
  if (errorLower.includes('file size') || 
      errorLower.includes('too large') || 
      errorLower.includes('exceeds') ||
      errorLower.includes('limit')) {
    return {
      code: UploadErrorCode.FILE_SIZE_ERROR,
      message: errorMessage,
      originalError: error
    };
  }

  // 默认未知错误
  return {
    code: UploadErrorCode.UNKNOWN_ERROR,
    message: errorMessage,
    originalError: error
  };
}

/**
 * 获取用户友好的错误信息键名
 * @param errorCode 错误代码
 * @returns 翻译键名
 */
export function getErrorTranslationKey(errorCode: UploadErrorCode): string {
  const keyMap = {
    [UploadErrorCode.NETWORK_ERROR]: 'modelDialog.errors.networkError',
    [UploadErrorCode.FILE_TYPE_ERROR]: 'modelDialog.errors.fileTypeError',
    [UploadErrorCode.FILE_SIZE_ERROR]: 'modelDialog.errors.fileSizeError',
    [UploadErrorCode.SERVER_ERROR]: 'modelDialog.errors.serverError',
    [UploadErrorCode.PERMISSION_ERROR]: 'modelDialog.errors.permissionError',
    [UploadErrorCode.CONFIG_ERROR]: 'modelDialog.errors.configError',
    [UploadErrorCode.UNKNOWN_ERROR]: 'modelDialog.errors.unknownError',
    [UploadErrorCode.MISSING_FIELDS]: 'modelDialog.errors.missingFields'
  };

  return keyMap[errorCode] || 'modelDialog.errors.unknownError';
}

/**
 * 验证文件的客户端检查
 * @param file 文件对象
 * @returns 验证结果
 */
export function validateFile(file: File): { valid: boolean; error?: UploadError } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: {
        code: UploadErrorCode.FILE_TYPE_ERROR,
        message: `Unsupported file type: ${file.type}`
      }
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: {
        code: UploadErrorCode.FILE_SIZE_ERROR,
        message: `File size ${file.size} exceeds limit of ${maxSize}`
      }
    };
  }

  return { valid: true };
}

/**
 * 验证必填字段
 * @param data 数据对象
 * @param requiredFields 必填字段列表
 * @returns 验证结果
 */
export function validateRequiredFields(
  data: Record<string, any>, 
  requiredFields: string[]
): { valid: boolean; missingFields?: string[]; error?: UploadError } {
  const missingFields = requiredFields.filter(field => {
    const value = data[field];
    return !value || (typeof value === 'string' && !value.trim());
  });

  if (missingFields.length > 0) {
    return {
      valid: false,
      missingFields,
      error: {
        code: UploadErrorCode.MISSING_FIELDS,
        message: `Missing required fields: ${missingFields.join(', ')}`
      }
    };
  }

  return { valid: true };
}
