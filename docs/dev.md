# 开发模型
Cursor中采取claude-4-sonnet MAX 模型

# 开发提示词

## 1. 增加导致
```text
@navbar_config.tsx 新增一个/outfit导航，导航的英文名是Outfix，中文名是服装上身，并同时新增一个对应的空白页面
```

## 2. 修改登录
修改/auth/register和登录弹框的UI样式，移除掉邮箱密码登录注册，以及Github登陆注册逻辑，只保留Google的登录的样式 

## 3. 支付功能Stripe
按照文档手动配置和测试

## 4. 前端开发改造
### 4.1 主题切换
主题高级自定义，选择tweakcn，https://tweakcn.com/editor/theme，切换主题为Neo Brutalism
点击Code复制pnpm dlx shadcn@latest add https://tweakcn.com/r/themes/neo-brutalism.json，命令行执行后，global.css会自动增加样式

### 4.2 落地页修页
```text
@src/app/[locale]/marketing/home/<USER>/app/[locale]/marketing/pricing @src/app/marketing/[locale]/blog 帮我修改这几个页面下的所有文案，包括遍历页面到的所有组件，让文案符合我的产品。我要开发一个AI试衣服的产品，名称为OutfitAI，文案修改要求：
1. 文案修改要体现OutfixAI的品牌名称，贴近我的产品功能
2. 需要同时修改@messages 下的中英文文案，并在组件中引入国际化
3. 文案中需要强调产品的AI功能
```
### 4.3 favicon替换
favicon替换： https://favicon.io/favicon-converter/

### 4.4 首页替换
@hero 用事先生成的两张图片，放以/public/marketing文件夹下，替换暗黑和明亮下的两张图片

### 5 服装上身
### 5.1 json数据
创建mock的Json数据，存放在根目录下

### 5.2 服装上身
```teXT
根据 @data.json 的数据，完成 @src/app/[locale]/marketing/outfit/page.tsx 这个页面，这个页面能够展示对应的穿搭风格，需求如下：
1. 页面顶部右上角有支持筛选（使用icon），分别表示全部/男性/女性，默认全部
2. @data.json 中的数据对应的套装的数据，在 @page.tsx 页面用卡片展示，卡片显示 url中的照片。用户点击后，能够以轮播图的方式展示split_images中的照片，split_images中的数据对应衣服。轮播图下方有两个按钮：一个按钮为男女类型按钮，点击跳转到amazon_url（打开新tab）；另一个按钮为试衣服，点击在控制台打印图片url就好。
3. 静态文案部分都需要实现国际化处理
4. 该页面的数据，应该引入 @data.json的数据
```

图片无法找到错误:修改next.config.js
```code
{
    protocol:'hhtps',
    hosthname:'static-main.aiyeshi.cn'
}
```
### 6 核心试衣功能
需要登录的页面：位置src/app/[locale]/(protected),都在这个文件夹下，后台管理里页面在它的admin目录下
模特数据:mock、models.json，
页面结果参照图：docs/fitting.png
https://bailian.console.aliyun.com/?spm=a2c4g.11186623.0.0.1256c483fEN0K2&tab=model#/efm/model_experience_center/vision?currentTab=imageGenerate&modelId=aitryon-plus

### 6.1 模特后台管理
首先创建一个 src/app/[locale]/(protected)/fitting-room/page.tsx 的空白文件，截图给到图片，然后输入以下提示词：

``` text
1. 在 @dashboard-sidebar.tsx 新增 FittingRoom页面，该页面对应 @page.tsx
2. 请你参考图片，完成 @page.tsx 这个页面的前端开发工作，左侧是操作面板，右侧是空白区域。用户可以选择对应的模特，当点击AI试衣模特的时候，右侧空白区域出现模特选择。模特的数据引入 @models.json 的数据。不区分女模和男模。同时这个页面有一个新增模特按钮，用户点击后弹出一个弹框，弹框中可以上传模特图片，并填入相关信息，确认后可以上传模特。
3. 试穿服装的筛选卡有两个选项：上下装、单件连体。如果是上下装，出现两个图片上传框，如果是单件连体，出现一个图片上传框。
4. 用户点击开始试衣服后，右侧空白区域需要有一个好看的加载动画，表示正在试衣服中
5. 所有的前端静态文案，需要考虑国际化 @/messages
```

### 6.2 图片上传存储
存储：https://www.aliyun.com/product/oss  阿里云存储
创建Bucket：https://oss.console.aliyun.com/bucket
OSS SDK： https://help.aliyun.com/zh/oss/developer-reference/getting-started-with-oss-sdk-for-node-js?spm=a2c4g.11186623.help-menu-31815.d_5_2_10_2.497732eferj2Ks&scm=20140722.H_32070._.OR_help-T_cn~zh-V_1

开发文档：快速入门+，放于docs/aiyun-oss.md

提示词：完成衣服（上衣和下装）的上传、模特的上传
此步问题：多个上传表单，上传异常要单独处理；ali-oss的安装包依赖，也要单独处理。
重大影响：AI修改的.env的storage的全局配置，将a3改成了ali-oss，可能存在隐患 


```text
新增后端上传接口，完成图片上传功能，包括模特图片的上传和用户衣服的上传。使用ali-oss的node.js sdk。参考下面的代码

const OSS = require('ali-oss'); 

const client = new OSS({
   //从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
   accessKeyId: process.env.OSS_ACCESS_KEY_ID,
   accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
   // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
   region: 'oss-cn-beijing',
   // 使用V4签名算法
   authorizationV4: true,
   // yourBucketName填写Bucket名称。
   bucket: 'huiyingbucket',
   // yourEndpoint填写Bucket所在地域对应的公网Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
   endpoint: 'https://oss-cn-beijing.aliyuncs.com',
});

SDK具体使用逻辑可以参考 @docs/aliyun-oss.md，要求接口返回对应的图片访问地址
```

### 6.3 模特数据的编辑
输入对应的提示词，要求AI创建一张表，用来保存对应的模特信息。并让他完成对应的自定义模特的逻辑
问题：数据库保存有下拉菜单选择的英文值，国际化中文，要单独处理

- **提示词**
1. 帮我新建一张表，用于保存用户自定义创建的模特信息  
2. 新建一个接口，完成用户保存自定义模特的逻辑  
3. /fitting页面加载时，需要自动获取数据中是否有自定义模特数据，如果有放到默认的模特前面  



### 6.4 列表页试穿

1. 点击按钮跳转到 /fitting-room 页面
2. 同时能够自动的回显图片的链接，如果 type:1 就是一件衣服，如果 type:2 就是两件衣服
梳理好整个流程后，我们就可以来完成该核心功能的开发了。

- 提示词:
``` txt
@page.tsx 完成该页面点击Try On按钮的逻辑，要求：
1. 点击该按钮后，跳转到 /fitting-room页面。携带上对应的id
2. 在 /fitting-room检测到有id后，通过id获取到对应的套装类型，type:1表示是一件衣服，选择one-piece。type:2表示是两件衣服，选择Top&Bottom。
3. 然后获取split_images中的数据，回显url的图片。并且将该图片url在之后试衣服的时候，作为衣服图片提交给AI
``` 


## 7.模AI试衣-基础版API参考

### 7.1 阿里百练试穿AI接入 
阿里百炼试衣：https://help.aliyun.com/zh/model-studio/outfitanyone-api
创建api-key：https://bailian.console.aliyun.com/?tab=model#/api-key
接口文档：https://bailian.console.aliyun.com/?tab=api#/api/?type=model&url=https%3A%2F%2Fhelp.aliyun.com%2Fdocument_detail%2F2881846.html，生成开发文档，docs/ai-api.md

接下来我们就可以输入提示词，让AI来帮我我们实现最核心的AI试衣服功能了：
- 提示词:ai试衣

``` txt
请你参考 @ai-tryon_api.md 的文档，实现AI试衣功能，要求：
1.增加对应的后端接口  
2.前端获取对应的模特图片以及衣服图片，调用后端接口（如果没有选择模特、没有上传衣服，不允许上传）  
3.等待AI生成试衣服结果的过程中，需要有一个加载动画和骨架图   
4.AI试衣服成功后，在右侧侧边显示试衣服图片  
```

### 7.2 试衣历史记录
- 提示词:试衣历史结果的存储
``` txt
1. 清除 `@/app/[locale]/(protected)/history/page.tsx` 页面的现有样式，该页面是用来保存用户的试衣历史
2. 请你创建对应的试衣服历史的数据表
3. 在@fitting页面，添加一个保存按钮，用户点击后能将数据保存数据到数据表中
4. 用户点击保存时，需要将AI生成的试衣结果的URL重新获取，保存到我们的阿里云bucket当中，使用@oss-client.ts 。最后保存bucket返回的url。不能直接保存AI结果的url,因为AI结果的url有效期只有24小时
```

### 8 付费限制
- 提示词:
``` txt
1. @fitting-room/page.tsx 增加付费限制，只有付费用户才能点击Start Try-On按钮实现试衣服，未付费用户该按钮禁用，并且后端将口也需要限制，增加校验用户是否处于订阅周期内的逻辑。如果不是订阅用户需要有个友好的提示，告诉用户该功能只是付费用户专享。

2. @fitting-room/page.tsx 检查页面中静态文案中，没有进行i8n国际化的地方，进行修改实现国际处理。

```


